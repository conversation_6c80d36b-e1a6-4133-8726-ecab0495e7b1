import bloodDonationService from "../services/bloodDonationService";
import { toast } from "../utils/toastUtils";

/**
 * Custom hook for handling doctor donor management actions (save, update, etc.)
 */
export const useDoctorDonorActions = () => {





  // Save donor info update (Doctor Update Modal)
  const handleSaveUpdate = async (selectedDonor, updateData, currentUser, onSuccess, setShowUpdateModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    console.log('💾 Starting handleSaveUpdate with:', {
      selectedDonorId: selectedDonor.id,
      selectedDonorProcess: selectedDonor.process,
      updateData: updateData,
      currentUserId: currentUser?.id
    });

    try {
      // Determine if this is a blood testing update or health examination update
      // Use blood testing update if we're at process step 4 (blood testing stage) OR if blood test data is provided
      const isBloodTestingStage = updateData.process === 4 || selectedDonor.process === 4;
      const hasBloodTestData = updateData.bloodGroup && updateData.rhType;
      const shouldUseBloodTestingUpdate = isBloodTestingStage || hasBloodTestData;

      if (shouldUseBloodTestingUpdate) {
        // Blood testing update (Stage 2)
        try {
          // Use the donation date from the form, or current date as fallback
          const donationDate = updateData.donationDate
            ? new Date(updateData.donationDate).toISOString()
            : new Date().toISOString();

          console.log('🩸 Performing blood testing update with data:', {
            isBloodTestingStage: isBloodTestingStage,
            hasBloodTestData: hasBloodTestData,
            bloodGroup: updateData.bloodGroup,
            rhType: updateData.rhType,
            donationDate: donationDate,
            notes: updateData.notes,
            doctorId: currentUser?.id || 4
          });

          // Use the blood testing update endpoint directly - pass all updateData
          await bloodDonationService.doctorBloodTestingUpdate(selectedDonor.id, {
            ...updateData, // Include all health information fields
            bloodGroup: updateData.bloodGroup,
            rhType: updateData.rhType,
            donationDate: donationDate,
            doctorId: currentUser?.id || 4,
            notes: updateData.notes || ""
          });

        } catch (bloodTestingError) {
          console.error("Failed to update blood testing results:", bloodTestingError);
          throw bloodTestingError; // Re-throw to show proper error message
        }
      } else {
        // Health examination update (Stage 1) - exclude blood testing fields
        const healthExamData = { ...updateData };
        delete healthExamData.bloodGroup;
        delete healthExamData.rhType;
        delete healthExamData.donationDate;

        await bloodDonationService.doctorHealthExaminationUpdate(selectedDonor.id, {
          ...healthExamData,
          doctorId: currentUser?.id || 4
        });
      }

      // 3. Skip weight/height update for now due to API validation issues
      // TODO: Fix PUT /api/Information/{id} API validation requirements
      /*
      if (updateData.weight || updateData.height) {
        try {
          await bloodDonationService.updateUserInformation(selectedDonor.userId, {
            weight: updateData.weight,
            height: updateData.height
          });
        } catch (weightHeightError) {
          console.warn("Failed to update weight/height, but continuing:", weightHeightError);
          // Don't throw error - weight/height update is optional
        }
      }
      */

      // Call success callback to refresh data
      if (onSuccess) {
        onSuccess();
      }

      setShowUpdateModal(false);
      setSelectedDonor(null);
      toast.success("Cập nhật thông tin thành công!");
    } catch (error) {
      console.error("Failed to update donor information:", error);
      toast.error(" Có lỗi xảy ra khi cập nhật thông tin!");
    }
  };

  // Save status update
  const handleSaveStatusUpdate = async (selectedDonor, statusUpdateData, currentUser, onSuccess, setShowStatusModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      const statusToSend = typeof statusUpdateData.status === 'string' ?
        parseInt(statusUpdateData.status) : statusUpdateData.status;

      const processToSend = typeof statusUpdateData.process === 'string' ?
        parseInt(statusUpdateData.process) : statusUpdateData.process;



      // 1. Update appointment status using PATCH /api/Appointment/{id}/status/{status}
      if (statusToSend && statusToSend !== selectedDonor.status) {
        await bloodDonationService.updateAppointmentStatus(
          selectedDonor.id,
          statusToSend,
          null, // Don't send notes with status update
          selectedDonor.userId
        );

        // Backend will create notification, frontend formatter will make it user-friendly
      }

      // 2. Update appointment process using PATCH /api/Appointment/{id}/process/{process}
      if (processToSend && processToSend !== selectedDonor.process) {
        await bloodDonationService.updateAppointmentProcess(
          selectedDonor.id,
          processToSend,
          null, // Don't send notes with process update
          selectedDonor.userId
        );

        // Backend will create notification, frontend formatter will make it user-friendly
      }

      // 3. Always update notes (even if empty to clear previous notes)
      // NOTE: We don't create notification for notes update as per user request
      try {
        await bloodDonationService.updateAppointmentNotes(
          selectedDonor.id,
          statusUpdateData.notes || ""
        );

      } catch (notesError) {
        console.error(' Failed to update notes:', notesError);
        // Don't throw error - continue with other operations
      }

      // 4. Call success callback to refresh data from server
      if (onSuccess) {
        onSuccess();
      }

      setShowStatusModal(false);
      setSelectedDonor(null);
      toast.success(" Cập nhật trạng thái thành công!");

      // NOTE: Custom notifications are now created above in the update logic
      // No need for additional notifications here to avoid duplicates
    } catch (error) {
      console.error("Error updating donor status:", error);
      toast.error(" Có lỗi xảy ra khi cập nhật trạng thái!");
    }
  };

  return {
    handleSaveUpdate,
    handleSaveStatusUpdate,
  };
};
